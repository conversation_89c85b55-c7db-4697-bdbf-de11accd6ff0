#!/usr/bin/env python3
from PIL import Image, ImageDraw, ImageFont
import os

# Create a simple window icon
size = 256
image = Image.new('RGBA', (size, size), (0, 0, 0, 0))
draw = ImageDraw.Draw(image)

# Draw window frame
frame_color = (100, 100, 100, 255)
window_color = (200, 200, 200, 255)
margin = 20

# Outer frame
draw.rectangle([margin, margin, size-margin, size-margin], fill=frame_color)
# Inner window
draw.rectangle([margin+10, margin+30, size-margin-10, size-margin-10], fill=window_color)

# Title bar
draw.rectangle([margin, margin, size-margin, margin+30], fill=(80, 80, 80, 255))

# Window controls (red, yellow, green dots)
dot_y = margin + 15
draw.ellipse([margin+10, dot_y-5, margin+20, dot_y+5], fill=(255, 95, 86, 255))
draw.ellipse([margin+25, dot_y-5, margin+35, dot_y+5], fill=(255, 189, 46, 255))
draw.ellipse([margin+40, dot_y-5, margin+50, dot_y+5], fill=(39, 201, 63, 255))

# Draw grid lines to represent multiple windows
grid_color = (150, 150, 150, 255)
for i in range(1, 3):
    x = margin + 10 + (size - 2*margin - 20) * i // 3
    draw.line([x, margin+30, x, size-margin-10], fill=grid_color, width=2)
    y = margin + 30 + (size - margin - 40) * i // 3
    draw.line([margin+10, y, size-margin-10, y], fill=grid_color, width=2)

image.save('window_icon.png')