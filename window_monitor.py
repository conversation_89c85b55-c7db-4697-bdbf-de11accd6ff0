#!/usr/bin/env python3
import subprocess
import time
import os
import sys
from pathlib import Path

WINDOW_THRESHOLD = int(os.environ.get('WINDOW_THRESHOLD', 10))
CHECK_INTERVAL = int(os.environ.get('CHECK_INTERVAL', 30))

script_dir = Path(__file__).parent
applescript_path = script_dir / "count_windows.applescript"

def count_windows():
    result = subprocess.run(
        ['osascript', str(applescript_path)],
        capture_output=True,
        text=True
    )
    
    output = result.stdout.strip()
    parts = output.split('||')
    window_count = int(parts[0])
    window_details = parts[1] if len(parts) > 1 else ""
    
    return window_count, window_details

def show_alert(window_count, threshold, window_details):
    alert_message = f"You have {window_count} windows open (limit: {threshold})"
    
    if window_details:
        window_list = window_details.replace('|', '\\n')
        alert_script = f'''
        display alert "{alert_message}" message "{window_list}" buttons {{"OK"}} default button "OK"
        '''
    else:
        alert_script = f'''
        display alert "{alert_message}" buttons {{"OK"}} default button "OK"
        '''
    
    subprocess.run(['osascript', '-e', alert_script])

def main():
    print(f"Window Monitor started. Threshold: {WINDOW_THRESHOLD}, Check interval: {CHECK_INTERVAL}s")
    
    while True:
        window_count, window_details = count_windows()
        print(f"Current window count: {window_count}")
        
        if window_count > WINDOW_THRESHOLD:
            show_alert(window_count, WINDOW_THRESHOLD, window_details)
        
        time.sleep(CHECK_INTERVAL)

if __name__ == "__main__":
    main()