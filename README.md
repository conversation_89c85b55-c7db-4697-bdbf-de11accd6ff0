# Mac Window Monitor

A simple macOS app that monitors the number of open windows and alerts you when you exceed a threshold.

## Files
- `count_windows.applescript` - Counts all open windows across all applications
- `window_monitor.py` - Main monitoring script that checks window count periodically
- `start_monitor.sh` - Launch script to start the monitor
- `window_icon.png` - App icon
- `create_icon.py` - <PERSON><PERSON><PERSON> to generate the icon

## Usage

1. Run the monitor:
   ```bash
   ./start_monitor.sh
   ```

2. Configure (optional):
   ```bash
   export WINDOW_THRESHOLD=15  # Default is 10
   export CHECK_INTERVAL=30    # Default is 10 seconds
   ./start_monitor.sh
   ```

3. Stop the monitor: Press Ctrl+C

## Features
- Counts windows across all visible applications
- Shows sticky alert notifications (not banners)
- Configurable threshold and check interval
- Displays current window count in console

## Alert Format
"You have X windows open (limit: Y)"