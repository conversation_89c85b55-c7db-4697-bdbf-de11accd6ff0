on run
    tell application "System Events"
        set windowCount to 0
        set windowList to {}
        set appList to name of every application process whose visible is true
        
        repeat with appName in appList
            try
                tell application process appName
                    set appWindowCount to count of windows
                    if appWindowCount > 0 then
                        set windowCount to windowCount + appWindowCount
                        set end of windowList to appName & ": " & appWindowCount
                    end if
                end tell
            end try
        end repeat
        
        set AppleScript's text item delimiters to "|"
        set windowInfo to windowList as string
        set AppleScript's text item delimiters to ""
        
        return windowCount & "||" & windowInfo
    end tell
end run